#!/usr/bin/env python3
"""
Simple test script to debug transcription issues
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def test_whisper_import():
    """Test if Whisper can be imported and loaded."""
    try:
        import whisper
        logging.info("✓ Whisper imported successfully")
        
        # Try loading a small model first
        logging.info("Loading Whisper base model...")
        model = whisper.load_model("base")
        logging.info("✓ Whisper model loaded successfully")
        
        return model
    except Exception as e:
        logging.error(f"✗ Error with Whisper: {e}")
        return None

def test_yt_dlp_import():
    """Test if yt-dlp can be imported."""
    try:
        import yt_dlp
        logging.info("✓ yt-dlp imported successfully")
        return True
    except Exception as e:
        logging.error(f"✗ Error with yt-dlp: {e}")
        return False

def test_audio_file_transcription(audio_file_path: str):
    """Test transcribing a specific audio file."""
    audio_path = Path(audio_file_path)
    
    if not audio_path.exists():
        logging.error(f"Audio file does not exist: {audio_path}")
        return False
    
    logging.info(f"Testing transcription of: {audio_path.name}")
    logging.info(f"File size: {audio_path.stat().st_size} bytes")
    
    model = test_whisper_import()
    if not model:
        return False
    
    try:
        logging.info("Starting transcription...")
        result = model.transcribe(str(audio_path))
        
        segments = result.get('segments', [])
        text = result.get('text', '')
        
        logging.info(f"Transcription completed!")
        logging.info(f"Number of segments: {len(segments)}")
        logging.info(f"Total text length: {len(text)} characters")
        
        if text:
            logging.info(f"First 100 characters: {text[:100]}...")
        
        if segments:
            logging.info(f"First segment: {segments[0]}")
        
        return True
        
    except Exception as e:
        logging.error(f"Error during transcription: {e}")
        return False

def main():
    """Main test function."""
    logging.info("=== Transcription Test Script ===")
    
    # Test imports
    test_whisper_import()
    test_yt_dlp_import()
    
    # Test with a specific audio file if provided
    audio_file = input("Enter path to audio file to test (or press Enter to skip): ").strip()
    if audio_file:
        test_audio_file_transcription(audio_file)
    
    logging.info("=== Test Complete ===")

if __name__ == '__main__':
    main()
