- The objective of this project is to scrape you tube videos page, update a list, use said list to download audio, transcribe, and posts-process said transcripts, all done in a folder of transcripts.
- Terms:
	- [Main Folder] - a folder containing the [InputURL] file and [Transcript] files
	- [Video URL] - a URL to the video online
	- [InputURL] - a .txt file containing a list (new line delimited) of YouTube URLs
	- [Video] - a full video file that is sometimes found in [Main Folder]
	- [Audio-Only] - audio files created by the yt_dlp module
	- [Transcript] - an .md file containing transcribed audio. The [Transcript] contains the following elements. Refer to [Transcript] prompt for hints on it's generation
	- [Video Name] - the name of the video and/or transcript
	- [Video Date] - the date of the video
	- [Video Author] - the author of the video and/or transcript
	- [Chunk Title] - a title given to a particular chunk in [Transcript]. Typically no more than 15-20 words.
	- [Timestamp URL] - a video URL including a timestamp. Opening the URL will go to the video at that particular time. (example: https://youtu.be/PKEwdWYLL5A&t=869)
- Perform the following:
	- Let user browse to the [Main Folder]
		- Put in a hardcoded variable containing a path to the folder. 
		- If the variable contains a path to a folder use that folder.
		- If it is empty then let the user browse to the folder.
	- Search inside folder for [InputURL] text file
	- For each line entry in [InputURL] get the [Video Name] and check if the there is an [Transcript] of the same name
		- If there is a [Transcript] containing the same name as the [InputURL] entry, delete the entry and move on the next.
		- If there isn't get the [Audio-Only] file
		- Then generate the [Transcript]
		- If a local LLM is available generate the [Chunk Title]
		- After the [Transcript] is completed, delete the [Audio-Only] and then delete the [InputURL] entry