import os
import whisper
from tkinter import filedialog, Tk
from datetime import timedelta
import subprocess
import tempfile
import shutil

VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi']
TRANSCRIPT_FORMAT = 'txt'
CHUNK_SECONDS = 30

model = whisper.load_model("medium")

def get_audio_tracks(video_path):
    """Returns a list of audio stream indices."""
    result = subprocess.run(
        ['ffprobe', '-v', 'error', '-select_streams', 'a', '-show_entries',
         'stream=index', '-of', 'csv=p=0', video_path],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False
    )
    if result.returncode != 0:
        print(f"Warning: ffprobe failed for {video_path}. Error: {result.stderr}")
        return []
    lines = result.stdout.strip().split('\n')
    tracks = []
    for line in lines:
        try:
            index = int(line.strip())
            tracks.append(index)
        except ValueError:
            continue
    return tracks

def extract_audio_track(video_path, stream_index, temp_dir):
    """Extracts a specific audio stream by stream index."""
    audio_path = os.path.join(temp_dir, f'track_{stream_index}.wav')
    result = subprocess.run(
        ['ffmpeg', '-y', '-i', video_path, '-map', f'0:{stream_index}',
         '-ac', '1', '-ar', '16000', audio_path],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False
    )
    if result.returncode != 0:
        print(f"Warning: Failed to extract audio stream index {stream_index} from {video_path}. Error: {result.stderr}")
        return None
    if not os.path.exists(audio_path) or os.path.getsize(audio_path) == 0:
        print(f"Warning: Extracted audio file for stream {stream_index} is missing or empty: {audio_path}")
        return None
    return audio_path

def choose_folder():
    root = Tk()
    root.withdraw()
    folder_selected = filedialog.askdirectory(title="Select folder containing video files")
    return folder_selected

def format_time(seconds):
    td = timedelta(seconds=int(seconds))
    total_seconds = int(td.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f'{hours:02}:{minutes:02}:{seconds:02}'

def transcribe_video(video_path):
    """Transcribes audio tracks from a video file."""
    print(f"Processing: {video_path}")
    track_indices = get_audio_tracks(video_path)

    if not track_indices:
        print(f"No audio tracks found or ffprobe failed for {video_path}.")
        return

    num_tracks = len(track_indices)
    base_name = os.path.splitext(video_path)[0]

    with tempfile.TemporaryDirectory() as temp_dir:
        for idx in track_indices:
            print(f"Extracting audio track with index {idx}...")
            audio_path = extract_audio_track(video_path, idx, temp_dir)

            if audio_path and os.path.exists(audio_path):
                print(f"Transcribing audio track index {idx} from {audio_path}...")
                try:
                    result = model.transcribe(audio_path, fp16=False)
                except Exception as e:
                    print(f"Error during transcription for track {idx} of {video_path}: {e}")
                    continue

                track_chunks = {}
                for segment in result.get('segments', []):
                    start_time = segment.get('start')
                    text = segment.get('text', '').strip()
                    if start_time is not None and text:
                        try:
                           chunk_index = int(start_time) // CHUNK_SECONDS
                           track_chunks.setdefault(chunk_index, []).append(text)
                        except ValueError:
                           print(f"Warning: Invalid start time '{segment['start']}' in segment for track {idx}. Skipping.")

                if num_tracks > 1:
                    output_path = f"{base_name} (track {idx}).{TRANSCRIPT_FORMAT}"
                else:
                    output_path = f"{base_name}.{TRANSCRIPT_FORMAT}"

                print(f"Writing transcript to: {output_path}")
                try:
                    with open(output_path, 'w', encoding='utf-8') as f:
                        if not track_chunks:
                             print(f"No segments transcribed for track {idx}.")
                             f.write(f"{format_time(0)}\n")
                             continue

                        max_chunk = max(track_chunks.keys())
                        for i in range(max_chunk + 1):
                            timestamp = format_time(i * CHUNK_SECONDS)
                            chunk_text = " ".join(track_chunks.get(i, []))
                            f.write(f"{timestamp} {chunk_text}\n")
                except IOError as e:
                    print(f"Error writing transcript file {output_path}: {e}")
                except Exception as e:
                     print(f"An unexpected error occurred while writing transcript for track {idx}: {e}")

            else:
                print(f"Skipping transcription for track index {idx} due to extraction failure or empty file.")

def main():
    folder = choose_folder()
    if not folder:
        print("No folder selected. Exiting.")
        return

    for file in os.listdir(folder):
        path = os.path.join(folder, file)
        if os.path.isfile(path) and os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
             transcribe_video(path)
        elif not os.path.isfile(path):
             print(f"Skipping non-file item: {file}")

if __name__ == '__main__':
    main()