#!/usr/bin/env python3
"""
Test script to verify date formatting functionality
"""

import logging
import random
from datetime import datetime
from typing import Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def format_upload_date_with_time(upload_date: str, timestamp: Optional[float], upload_time: Optional[str]) -> str:
    """
    Format upload date to YYYYMMDDHHmm if time is available, 
    otherwise YYYYMMDDXXXX where XXXX is random number 0000-2359.
    """
    if not upload_date:
        return ''
    
    try:
        # Start with the base date (YYYYMMDD format from yt-dlp)
        if len(upload_date) >= 8:
            base_date = upload_date[:8]  # YYYYMMDD
        else:
            return upload_date  # Return as-is if format is unexpected
        
        # Try to extract hour and minute from various sources
        hour = None
        minute = None
        
        # Method 1: Try timestamp (Unix timestamp)
        if timestamp:
            try:
                dt = datetime.fromtimestamp(timestamp)
                hour = dt.hour
                minute = dt.minute
                logging.info(f"Extracted time from timestamp: {hour:02d}:{minute:02d}")
            except Exception as e:
                logging.debug(f"Failed to parse timestamp {timestamp}: {e}")
        
        # Method 2: Try upload_time string
        if hour is None and upload_time:
            try:
                # upload_time might be in format like "14:30" or "14:30:45"
                time_parts = upload_time.split(':')
                if len(time_parts) >= 2:
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    logging.info(f"Extracted time from upload_time: {hour:02d}:{minute:02d}")
            except Exception as e:
                logging.debug(f"Failed to parse upload_time {upload_time}: {e}")
        
        # If we have hour and minute, use them
        if hour is not None and minute is not None:
            # Validate hour and minute ranges
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                formatted_time = f"{hour:02d}{minute:02d}"
                result = f"{base_date}{formatted_time}"
                logging.info(f"Formatted date with actual time: {result}")
                return result
        
        # If no time available, generate random time (0000-2359)
        random_hour = random.randint(0, 23)
        random_minute = random.randint(0, 59)
        random_time = f"{random_hour:02d}{random_minute:02d}"
        result = f"{base_date}{random_time}"
        logging.info(f"Formatted date with random time: {result}")
        return result
        
    except Exception as e:
        logging.warning(f"Error formatting upload date {upload_date}: {e}")
        return upload_date  # Return original if formatting fails

def test_date_formatting():
    """Test various date formatting scenarios."""
    print("=== Testing Date Formatting ===\n")
    
    # Test cases
    test_cases = [
        # (upload_date, timestamp, upload_time, description)
        ("20240512", 1715529600.0, None, "With Unix timestamp"),
        ("20240512", None, "14:30", "With upload_time string"),
        ("20240512", None, "14:30:45", "With upload_time string (with seconds)"),
        ("20240512", 1715529600.0, "14:30", "With both timestamp and upload_time"),
        ("20240512", None, None, "No time info (should use random)"),
        ("", None, None, "Empty upload_date"),
        ("2024051", None, None, "Short upload_date"),
        ("20240512", -1, None, "Invalid timestamp"),
        ("20240512", None, "25:70", "Invalid upload_time"),
    ]
    
    for i, (upload_date, timestamp, upload_time, description) in enumerate(test_cases, 1):
        print(f"Test {i}: {description}")
        print(f"  Input: upload_date='{upload_date}', timestamp={timestamp}, upload_time='{upload_time}'")
        
        result = format_upload_date_with_time(upload_date, timestamp, upload_time)
        print(f"  Result: '{result}'")
        
        # Validate result format
        if result and len(result) == 12 and result.isdigit():
            date_part = result[:8]
            time_part = result[8:]
            hour = int(time_part[:2])
            minute = int(time_part[2:])
            
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                print(f"  ✓ Valid format: {date_part} {hour:02d}:{minute:02d}")
            else:
                print(f"  ✗ Invalid time: {hour:02d}:{minute:02d}")
        elif result == upload_date:
            print(f"  ✓ Returned original date as fallback")
        elif result == '':
            print(f"  ✓ Returned empty string for empty input")
        else:
            print(f"  ✗ Unexpected format")
        
        print()

def test_with_real_youtube_url():
    """Test with a real YouTube URL if yt-dlp is available."""
    try:
        import yt_dlp
        
        url = input("Enter a YouTube URL to test (or press Enter to skip): ").strip()
        if not url:
            return
        
        print(f"\n=== Testing with real YouTube URL ===")
        print(f"URL: {url}")
        
        ydl_opts = {'quiet': True, 'no_warnings': True}
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            
            upload_date = info.get('upload_date', '')
            timestamp = info.get('timestamp')
            upload_time = info.get('upload_time')
            
            print(f"Raw metadata:")
            print(f"  upload_date: {upload_date}")
            print(f"  timestamp: {timestamp}")
            print(f"  upload_time: {upload_time}")
            
            formatted = format_upload_date_with_time(upload_date, timestamp, upload_time)
            print(f"Formatted result: {formatted}")
            
    except ImportError:
        print("yt-dlp not available, skipping real URL test")
    except Exception as e:
        print(f"Error testing with real URL: {e}")

if __name__ == '__main__':
    test_date_formatting()
    test_with_real_youtube_url()
