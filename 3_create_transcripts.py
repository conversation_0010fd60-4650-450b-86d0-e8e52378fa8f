#!/usr/bin/env python3
"""
Audio File Transcriber for [Audio-Only] Folder
===============================================

This script transcribes all audio files found in the [Audio-Only] folder using
OpenAI's Whisper model and saves the transcripts to a [Transcript] folder.

Features:
- Processes all audio files in [Audio-Only] folder
- Uses hardcoded paths if they exist, otherwise prompts for folder selection
- Creates transcripts with YAML front matter
- Skips already transcribed files
- Progress tracking and error handling
- Supports multiple audio formats (m4a, mp3, wav, aac, ogg)

Requirements:
- openai-whisper: pip install openai-whisper
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, List
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import whisper
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install openai-whisper")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# Configuration
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\HEW Notes\Video Transcripts MD"
AUDIO_FOLDER_NAME = "audio"
TRANSCRIPT_FOLDER_NAME = "transcript"
SUPPORTED_AUDIO_EXTENSIONS = {'.m4a', '.mp3', '.wav', '.aac', '.ogg', '.flac', '.wma'}


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:200].strip()


def get_folder_path(folder_type="folder"):
    """Get folder path, use hardcoded path if it exists."""
    if os.path.exists(HARDCODED_FOLDER_PATH):
        return HARDCODED_FOLDER_PATH
    
    # Browse for folder if hardcoded path doesn't exist
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    folder_path = filedialog.askdirectory(
        title=f"Select base folder containing {folder_type}",
        initialdir=os.getcwd()
    )
    
    if not folder_path:
        messagebox.showwarning("No Folder Selected", "No folder was selected. Exiting.")
        return None
        
    return folder_path


def find_audio_files(audio_folder: Path) -> List[Path]:
    """Find all supported audio files in the audio folder."""
    audio_files = []
    
    if not audio_folder.exists():
        print(f"Error: Audio folder does not exist: {audio_folder}")
        return audio_files
    
    for file_path in audio_folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_AUDIO_EXTENSIONS:
            audio_files.append(file_path)
    
    return sorted(audio_files)


def create_transcript_folder(base_path: Path) -> Optional[Path]:
    """Create [Transcript] folder if it doesn't exist."""
    transcript_folder = base_path / TRANSCRIPT_FOLDER_NAME
    if not transcript_folder.exists():
        try:
            transcript_folder.mkdir(parents=True, exist_ok=True)
            print(f"Created transcript folder: {transcript_folder}")
        except OSError as e:
            print(f"Error creating transcript folder {transcript_folder}: {e}")
            return None
    return transcript_folder


def get_existing_transcripts(transcript_folder: Path) -> set:
    """Get set of existing transcript files to avoid re-transcribing."""
    existing_transcripts = set()
    
    if transcript_folder.exists():
        for file_path in transcript_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.md':
                # Remove extension and normalize for comparison
                name_without_ext = file_path.stem.lower()
                existing_transcripts.add(name_without_ext)
    
    return existing_transcripts


def transcribe_audio_file(audio_path: Path, transcript_folder: Path, model, existing_transcripts: set) -> bool:
    """Transcribe a single audio file using Whisper."""
    
    # Check if transcript already exists
    audio_name = sanitize_filename(audio_path.stem).lower()
    if audio_name in existing_transcripts:
        print(f"⏭️  Skipping (transcript exists): {audio_path.name}")
        return True
    
    print(f"🎵 Transcribing: {audio_path.name}")
    
    try:
        # Check file exists and has content
        if not audio_path.exists():
            print(f"❌ Audio file does not exist: {audio_path}")
            return False
        
        file_size = audio_path.stat().st_size
        if file_size == 0:
            print(f"❌ Audio file is empty: {audio_path}")
            return False
        
        print(f"   File size: {file_size:,} bytes")
        
        # Transcribe using Whisper
        print("   🔄 Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))
        
        # Extract results
        segments = result.get('segments', [])
        text = result.get('text', '').strip()
        
        print(f"   ✅ Transcription completed - {len(segments)} segments, {len(text)} characters")
        
        if not text:
            print(f"   ⚠️  No speech detected in {audio_path.name}")
            text = "[No speech detected]"
        
        # Create transcript file
        safe_title = sanitize_filename(audio_path.stem)
        transcript_path = transcript_folder / f"{safe_title}.md"
        
        # Extract basic metadata from filename
        title = audio_path.stem
        
        with open(transcript_path, 'w', encoding='utf-8') as f:
            # YAML front matter
            f.write("---\n")
            f.write(f"title: {title}\n")
            f.write(f"source_file: {audio_path.name}\n")
            f.write(f"transcription_model: whisper\n")
            f.write("---\n\n")
            
            # Write transcript content
            if not segments:
                f.write("- [00:00:00] - [No Content] - No speech detected\n\n")
            else:
                # Write segments with timestamps
                for segment in segments:
                    start_time = segment.get('start', 0)
                    text_content = segment.get('text', '').strip()
                    
                    # Format timestamp as HH:MM:SS
                    hours = int(start_time // 3600)
                    minutes = int((start_time % 3600) // 60)
                    seconds = int(start_time % 60)
                    timestamp = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    
                    if text_content:
                        f.write(f"- [{timestamp}] - {text_content}\n")
                
                f.write("\n")
                
                # Also write full transcript
                f.write("## Full Transcript\n\n")
                f.write(text)
                f.write("\n")
        
        print(f"   💾 Transcript saved: {transcript_path.name}")
        return True
        
    except Exception as e:
        print(f"❌ Transcription error for {audio_path.name}: {e}")
        logging.error(f"Transcription error for {audio_path}: {e}")
        return False


def main():
    """Main function to transcribe all audio files."""
    print("Audio File Transcriber for [Audio-Only] Folder")
    print("=" * 50)
    
    # Get base folder
    base_folder = get_folder_path("[Audio-Only] and [Transcript] folders")
    if not base_folder:
        return
    
    base_path = Path(base_folder)
    audio_folder = base_path / AUDIO_FOLDER_NAME
    
    # Check if audio folder exists
    if not audio_folder.exists():
        print(f"Error: {AUDIO_FOLDER_NAME} folder not found in {base_folder}")
        print(f"Expected path: {audio_folder}")
        return
    
    # Find audio files
    print(f"Scanning for audio files in: {audio_folder}")
    audio_files = find_audio_files(audio_folder)
    
    if not audio_files:
        print(f"No audio files found in {audio_folder}")
        print(f"Supported formats: {', '.join(SUPPORTED_AUDIO_EXTENSIONS)}")
        return
    
    print(f"Found {len(audio_files)} audio file(s):")
    for i, audio_file in enumerate(audio_files, 1):
        file_size = audio_file.stat().st_size
        print(f"  {i}. {audio_file.name} ({file_size:,} bytes)")
    
    # Create transcript folder
    transcript_folder = create_transcript_folder(base_path)
    if not transcript_folder:
        return
    
    print(f"\nTranscripts will be saved to: {transcript_folder}")
    
    # Get existing transcripts
    existing_transcripts = get_existing_transcripts(transcript_folder)
    if existing_transcripts:
        print(f"Found {len(existing_transcripts)} existing transcript(s)")
    
    # Load Whisper model
    print("\n🔄 Loading Whisper model...")
    try:
        model = whisper.load_model("base")  # Use base model for good balance of speed/accuracy
        print("✅ Whisper model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Whisper model: {e}")
        return
    
    # Process each audio file
    print(f"\nStarting transcription process...")
    print("-" * 50)
    
    successful_transcriptions = 0
    failed_transcriptions = 0
    skipped_transcriptions = 0
    
    for i, audio_file in enumerate(audio_files, 1):
        print(f"\n[{i}/{len(audio_files)}] Processing:")
        
        success = transcribe_audio_file(
            audio_file, 
            transcript_folder, 
            model, 
            existing_transcripts
        )
        
        if success:
            if sanitize_filename(audio_file.stem).lower() in existing_transcripts:
                skipped_transcriptions += 1
            else:
                successful_transcriptions += 1
                # Add to existing set to avoid re-processing in same session
                existing_transcripts.add(sanitize_filename(audio_file.stem).lower())
        else:
            failed_transcriptions += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("TRANSCRIPTION SUMMARY")
    print("=" * 50)
    print(f"✅ Successful transcriptions: {successful_transcriptions}")
    print(f"⏭️  Skipped (already exist): {skipped_transcriptions}")
    print(f"❌ Failed transcriptions: {failed_transcriptions}")
    print(f"📁 Audio folder: {audio_folder}")
    print(f"📁 Transcript folder: {transcript_folder}")
    
    if failed_transcriptions > 0:
        print(f"\n⚠️  {failed_transcriptions} transcriptions failed. Check the audio files and try again.")
    
    if successful_transcriptions > 0:
        print(f"\n🎉 Successfully transcribed {successful_transcriptions} audio files!")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
